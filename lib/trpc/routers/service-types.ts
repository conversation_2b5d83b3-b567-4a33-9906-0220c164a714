import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, like, count, sql, and, or } from "drizzle-orm";
import {
	createTRPCRouter as router,
	publicProcedure,
	protectedProcedure,
	protectedAdminProcedure,
} from "../trpc";
import { serviceTypes, users } from "@/lib/db/schema";
import {
	serviceTypeCreateSchema,
	serviceTypeUpdateSchema,
	serviceTypeFilterSchema,
	type ServiceType,
	type ServiceTypeWithCreator,
} from "@/lib/types/clinic";
import { logSlowQuery } from "./helpers/cat-helpers";

export const serviceTypesRouter = router({
	// Public procedures - for clinics to view available service types

	// Get all active service types (public)
	getAll: publicProcedure
		.input(serviceTypeFilterSchema.optional())
		.query(async ({ ctx, input = {} }) => {
			const startTime = performance.now();
			const { category, isActive = true, search, limit, offset } = input;

			let whereConditions = [eq(serviceTypes.isActive, isActive)];

			if (category) {
				whereConditions.push(eq(serviceTypes.category, category));
			}

			if (search) {
				whereConditions.push(
					or(
						like(serviceTypes.name, `%${search}%`),
						like(serviceTypes.description, `%${search}%`)
					)
				);
			}

			const whereClause =
				whereConditions.length > 0 ? and(...whereConditions) : undefined;

			const serviceTypesList = await ctx.db.query.serviceTypes.findMany({
				where: whereClause,
				orderBy: [asc(serviceTypes.displayOrder), asc(serviceTypes.name)],
				limit,
				offset,
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getServiceTypes", duration);

			return serviceTypesList as ServiceType[];
		}),

	// Get service types by category (public)
	getByCategory: publicProcedure
		.input(z.object({ category: z.string() }))
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { category } = input;

			const serviceTypesList = await ctx.db.query.serviceTypes.findMany({
				where: and(
					eq(serviceTypes.category, category),
					eq(serviceTypes.isActive, true)
				),
				orderBy: [asc(serviceTypes.displayOrder), asc(serviceTypes.name)],
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getServiceTypesByCategory", duration);

			return serviceTypesList as ServiceType[];
		}),

	// Get all categories (public)
	getCategories: publicProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		const categories = await ctx.db
			.selectDistinct({ category: serviceTypes.category })
			.from(serviceTypes)
			.where(eq(serviceTypes.isActive, true))
			.orderBy(asc(serviceTypes.category));

		const duration = performance.now() - startTime;
		logSlowQuery("getServiceTypeCategories", duration);

		return categories.map((c) => c.category);
	}),

	// Admin procedures - for managing service types

	// Get all service types with creator info (admin only)
	adminGetAll: protectedAdminProcedure
		.input(serviceTypeFilterSchema.optional())
		.query(async ({ ctx, input = {} }) => {
			const startTime = performance.now();
			const { category, isActive, search, limit, offset } = input;

			let whereConditions = [];

			if (category) {
				whereConditions.push(eq(serviceTypes.category, category));
			}

			if (isActive !== undefined) {
				whereConditions.push(eq(serviceTypes.isActive, isActive));
			}

			if (search) {
				whereConditions.push(
					or(
						like(serviceTypes.name, `%${search}%`),
						like(serviceTypes.description, `%${search}%`)
					)
				);
			}

			const whereClause =
				whereConditions.length > 0 ? and(...whereConditions) : undefined;

			const serviceTypesList = await ctx.db.query.serviceTypes.findMany({
				where: whereClause,
				with: {
					createdBy: {
						columns: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
				orderBy: [desc(serviceTypes.createdAt)],
				limit,
				offset,
			});

			const duration = performance.now() - startTime;
			logSlowQuery("adminGetServiceTypes", duration);

			return serviceTypesList as ServiceTypeWithCreator[];
		}),

	// Create service type (admin only)
	create: protectedAdminProcedure
		.input(serviceTypeCreateSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const userId = Number(ctx.user.id);

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Check if service type with same name already exists
			const existingServiceType = await ctx.db.query.serviceTypes.findFirst({
				where: eq(serviceTypes.name, input.name),
			});

			if (existingServiceType) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "A service type with this name already exists",
				});
			}

			// Create the service type
			const [newServiceType] = await ctx.db
				.insert(serviceTypes)
				.values({
					name: input.name,
					description: input.description,
					category: input.category,
					isActive: input.isActive,
					requiresAppointment: input.requiresAppointment,
					displayOrder: input.displayOrder,
					createdBy: userId,
					updatedAt: new Date(),
				})
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("createServiceType", duration);

			return newServiceType as ServiceType;
		}),

	// Update service type (admin only)
	update: protectedAdminProcedure
		.input(
			z.object({
				serviceTypeId: z.number(),
				data: serviceTypeUpdateSchema,
			})
		)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { serviceTypeId, data } = input;

			// Check if service type exists
			const existingServiceType = await ctx.db.query.serviceTypes.findFirst({
				where: eq(serviceTypes.id, serviceTypeId),
			});

			if (!existingServiceType) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service type not found",
				});
			}

			// If updating name, check for conflicts
			if (data.name && data.name !== existingServiceType.name) {
				const nameConflict = await ctx.db.query.serviceTypes.findFirst({
					where: eq(serviceTypes.name, data.name),
				});

				if (nameConflict) {
					throw new TRPCError({
						code: "CONFLICT",
						message: "A service type with this name already exists",
					});
				}
			}

			// Update the service type
			const [updatedServiceType] = await ctx.db
				.update(serviceTypes)
				.set({
					...data,
					updatedAt: new Date(),
				})
				.where(eq(serviceTypes.id, serviceTypeId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("updateServiceType", duration);

			return updatedServiceType as ServiceType;
		}),

	// Delete service type (admin only)
	delete: protectedAdminProcedure
		.input(z.object({ serviceTypeId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { serviceTypeId } = input;

			// Check if service type exists
			const existingServiceType = await ctx.db.query.serviceTypes.findFirst({
				where: eq(serviceTypes.id, serviceTypeId),
			});

			if (!existingServiceType) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service type not found",
				});
			}

			// Check if any clinics are using this service type
			const clinicsUsingService = await ctx.db.query.clinicServices.findFirst({
				where: eq(serviceTypes.id, serviceTypeId),
			});

			if (clinicsUsingService) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "Cannot delete service type that is being used by clinics. Deactivate it instead.",
				});
			}

			// Delete the service type
			await ctx.db.delete(serviceTypes).where(eq(serviceTypes.id, serviceTypeId));

			const duration = performance.now() - startTime;
			logSlowQuery("deleteServiceType", duration);

			return { success: true };
		}),

	// Toggle service type active status (admin only)
	toggleActive: protectedAdminProcedure
		.input(z.object({ serviceTypeId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { serviceTypeId } = input;

			// Get current service type
			const serviceType = await ctx.db.query.serviceTypes.findFirst({
				where: eq(serviceTypes.id, serviceTypeId),
			});

			if (!serviceType) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service type not found",
				});
			}

			// Toggle active status
			const [updatedServiceType] = await ctx.db
				.update(serviceTypes)
				.set({
					isActive: !serviceType.isActive,
					updatedAt: new Date(),
				})
				.where(eq(serviceTypes.id, serviceTypeId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("toggleServiceTypeActive", duration);

			return updatedServiceType as ServiceType;
		}),
});
