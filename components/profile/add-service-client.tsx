"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Loader2, Search, Plus, CheckCircle, Clock } from "lucide-react";
import { trpc } from "@/lib/trpc/client";
import { toast } from "sonner";
import {
	clinicServiceCreateSchema,
	type ClinicServiceCreateInput,
	type ServiceType,
} from "@/lib/types/clinic";

export function AddServiceClient() {
	const router = useRouter();
	const t = useTranslations("services");
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("all");
	const [selectedServiceType, setSelectedServiceType] = useState<ServiceType | null>(null);

	// Fetch available service types
	const {
		data: serviceTypes = [],
		isLoading: isLoadingServiceTypes,
		error: serviceTypesError,
	} = trpc.serviceTypes.getAll.useQuery({
		search: searchQuery,
		category: selectedCategory === "all" ? undefined : selectedCategory,
		isActive: true,
	});

	// Get unique categories for filtering
	const categories = Array.from(
		new Set(serviceTypes.map((st) => st.category))
	).sort();

	const form = useForm<ClinicServiceCreateInput>({
		resolver: zodResolver(clinicServiceCreateSchema),
		defaultValues: {
			serviceTypeId: 0,
			price: "",
			customDescription: "",
			isAvailable: true,
		},
	});

	const addServiceMutation = trpc.clinics.addService.useMutation({
		onSuccess: () => {
			toast.success(t("addService.success"));
			router.push("/profile/services");
		},
		onError: (error) => {
			toast.error(error.message);
		},
	});

	const onSubmit = async (data: ClinicServiceCreateInput) => {
		if (!selectedServiceType) {
			toast.error(t("addService.selectServiceType"));
			return;
		}

		try {
			await addServiceMutation.mutateAsync({
				...data,
				serviceTypeId: selectedServiceType.id,
			});
		} catch (error) {
			// Error handled in onError callback
		}
	};

	const handleServiceTypeSelect = (serviceType: ServiceType) => {
		setSelectedServiceType(serviceType);
		form.setValue("serviceTypeId", serviceType.id);
	};

	if (serviceTypesError) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center">
						<h3 className="text-lg font-medium text-red-600 mb-2">
							{t("addService.errorLoading")}
						</h3>
						<p className="text-muted-foreground">
							{serviceTypesError.message}
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			{/* Service Type Selection */}
			<Card>
				<CardHeader>
					<CardTitle>{t("addService.selectServiceType")}</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Search and Filter */}
					<div className="flex flex-col sm:flex-row gap-4">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
							<Input
								placeholder={t("addService.searchPlaceholder")}
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								className="pl-10"
							/>
						</div>
						<Select
							value={selectedCategory}
							onValueChange={setSelectedCategory}
						>
							<SelectTrigger className="w-full sm:w-48">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">{t("addService.allCategories")}</SelectItem>
								{categories.map((category) => (
									<SelectItem key={category} value={category}>
										{t(`categories.${category}`)}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Service Types Grid */}
					{isLoadingServiceTypes ? (
						<div className="flex justify-center py-8">
							<Loader2 className="h-6 w-6 animate-spin" />
						</div>
					) : serviceTypes.length === 0 ? (
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								{t("addService.noServiceTypes")}
							</p>
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{serviceTypes.map((serviceType) => (
								<Card
									key={serviceType.id}
									className={`cursor-pointer transition-all hover:shadow-md ${
										selectedServiceType?.id === serviceType.id
											? "ring-2 ring-primary"
											: ""
									}`}
									onClick={() => handleServiceTypeSelect(serviceType)}
								>
									<CardContent className="p-4">
										<div className="flex items-start justify-between mb-2">
											<h4 className="font-medium">{serviceType.name}</h4>
											{selectedServiceType?.id === serviceType.id && (
												<CheckCircle className="h-5 w-5 text-primary" />
											)}
										</div>
										<p className="text-sm text-muted-foreground mb-3">
											{serviceType.description}
										</p>
										<div className="flex flex-wrap gap-2">
											<Badge variant="secondary" className="text-xs">
												{t(`categories.${serviceType.category}`)}
											</Badge>
											{serviceType.requiresAppointment && (
												<Badge
													variant="outline"
													className="text-xs border-amber-500 text-amber-500"
												>
													<Clock className="h-3 w-3 mr-1" />
													{t("appointmentRequired")}
												</Badge>
											)}
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Service Configuration */}
			{selectedServiceType && (
				<Card>
					<CardHeader>
						<CardTitle>{t("addService.configureService")}</CardTitle>
					</CardHeader>
					<CardContent>
						<Form {...form}>
							<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									{/* Price */}
									<FormField
										control={form.control}
										name="price"
										render={({ field }) => (
											<FormItem>
												<FormLabel>{t("price")}</FormLabel>
												<FormControl>
													<Input
														placeholder={t("pricePlaceholder")}
														{...field}
														disabled={addServiceMutation.isPending}
													/>
												</FormControl>
												<FormDescription>
													{t("priceDescription")}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Availability */}
									<FormField
										control={form.control}
										name="isAvailable"
										render={({ field }) => (
											<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
												<FormControl>
													<Checkbox
														checked={field.value}
														onCheckedChange={field.onChange}
														disabled={addServiceMutation.isPending}
													/>
												</FormControl>
												<div className="space-y-1 leading-none">
													<FormLabel>{t("isAvailable")}</FormLabel>
													<FormDescription>
														{t("isAvailableDescription")}
													</FormDescription>
												</div>
											</FormItem>
										)}
									/>
								</div>

								{/* Custom Description */}
								<FormField
									control={form.control}
									name="customDescription"
									render={({ field }) => (
										<FormItem>
											<FormLabel>{t("customDescription")}</FormLabel>
											<FormControl>
												<Textarea
													placeholder={t("customDescriptionPlaceholder")}
													className="min-h-[100px]"
													{...field}
													disabled={addServiceMutation.isPending}
												/>
											</FormControl>
											<FormDescription>
												{t("customDescriptionDescription")}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Action Buttons */}
								<div className="flex gap-4">
									<Button
										type="button"
										variant="outline"
										onClick={() => router.push("/profile/services")}
										disabled={addServiceMutation.isPending}
									>
										{t("cancel")}
									</Button>
									<Button
										type="submit"
										disabled={addServiceMutation.isPending}
									>
										{addServiceMutation.isPending ? (
											<>
												<Loader2 className="h-4 w-4 mr-2 animate-spin" />
												{t("adding")}
											</>
										) : (
											<>
												<Plus className="h-4 w-4 mr-2" />
												{t("addService.submit")}
											</>
										)}
									</Button>
								</div>
							</form>
						</Form>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
