"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { trpc } from "@/lib/trpc/client";
import { toast } from "sonner";
import {
	serviceTypeUpdateSchema,
	type ServiceTypeUpdateInput,
	type ServiceTypeWithCreator,
	SERVICE_CATEGORIES,
} from "@/lib/types/clinic";

interface ServiceTypeEditDialogProps {
	serviceType: ServiceTypeWithCreator;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess: () => void;
}

export function ServiceTypeEditDialog({
	serviceType,
	open,
	onOpenChange,
	onSuccess,
}: ServiceTypeEditDialogProps) {
	const t = useTranslations("admin.serviceTypes");
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm<ServiceTypeUpdateInput>({
		resolver: zodResolver(serviceTypeUpdateSchema),
		defaultValues: {
			name: serviceType.name,
			description: serviceType.description,
			category: serviceType.category,
			isActive: serviceType.isActive,
			requiresAppointment: serviceType.requiresAppointment,
			displayOrder: serviceType.displayOrder,
		},
	});

	// Reset form when serviceType changes
	useEffect(() => {
		form.reset({
			name: serviceType.name,
			description: serviceType.description,
			category: serviceType.category,
			isActive: serviceType.isActive,
			requiresAppointment: serviceType.requiresAppointment,
			displayOrder: serviceType.displayOrder,
		});
	}, [serviceType, form]);

	const updateMutation = trpc.serviceTypes.update.useMutation({
		onSuccess: () => {
			toast.success(t("updateSuccess"));
			onSuccess();
		},
		onError: (error) => {
			toast.error(error.message);
		},
		onSettled: () => {
			setIsSubmitting(false);
		},
	});

	const onSubmit = async (data: ServiceTypeUpdateInput) => {
		setIsSubmitting(true);
		try {
			await updateMutation.mutateAsync({
				serviceTypeId: serviceType.id,
				data,
			});
		} catch (error) {
			// Error handled in onError callback
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			onOpenChange(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{t("editServiceType")}</DialogTitle>
					<DialogDescription>
						{t("editServiceTypeDescription")}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Name */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("name")}</FormLabel>
										<FormControl>
											<Input
												placeholder={t("namePlaceholder")}
												{...field}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Category */}
							<FormField
								control={form.control}
								name="category"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("category")}</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value}
											disabled={isSubmitting}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder={t("selectCategory")} />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{Object.entries(SERVICE_CATEGORIES).map(([key, value]) => (
													<SelectItem key={key} value={value}>
														{t(`categories.${value}`)}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Description */}
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("description")}</FormLabel>
									<FormControl>
										<Textarea
											placeholder={t("descriptionPlaceholder")}
											className="min-h-[100px]"
											{...field}
											disabled={isSubmitting}
										/>
									</FormControl>
									<FormDescription>
										{t("descriptionHelp")}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Display Order */}
							<FormField
								control={form.control}
								name="displayOrder"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("displayOrder")}</FormLabel>
										<FormControl>
											<Input
												type="number"
												min="0"
												placeholder="0"
												{...field}
												onChange={(e) => field.onChange(Number(e.target.value))}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											{t("displayOrderHelp")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Checkboxes */}
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="requiresAppointment"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={field.onChange}
													disabled={isSubmitting}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>{t("requiresAppointment")}</FormLabel>
												<FormDescription>
													{t("requiresAppointmentHelp")}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="isActive"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={field.onChange}
													disabled={isSubmitting}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>{t("isActive")}</FormLabel>
												<FormDescription>
													{t("isActiveHelp")}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>
							</div>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={handleClose}
								disabled={isSubmitting}
							>
								{t("cancel")}
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? t("updating") : t("update")}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
