"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { trpc } from "@/lib/trpc/client";
import { toast } from "sonner";
import type { ServiceTypeWithCreator } from "@/lib/types/clinic";

interface ServiceTypeDeleteDialogProps {
	serviceType: ServiceTypeWithCreator;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess: () => void;
}

export function ServiceTypeDeleteDialog({
	serviceType,
	open,
	onOpenChange,
	onSuccess,
}: ServiceTypeDeleteDialogProps) {
	const t = useTranslations("admin.serviceTypes");
	const [isDeleting, setIsDeleting] = useState(false);

	const deleteMutation = trpc.serviceTypes.delete.useMutation({
		onSuccess: () => {
			toast.success(t("deleteSuccess"));
			onSuccess();
		},
		onError: (error) => {
			toast.error(error.message);
		},
		onSettled: () => {
			setIsDeleting(false);
		},
	});

	const handleDelete = async () => {
		setIsDeleting(true);
		try {
			await deleteMutation.mutateAsync({
				serviceTypeId: serviceType.id,
			});
		} catch (error) {
			// Error handled in onError callback
		}
	};

	const handleClose = () => {
		if (!isDeleting) {
			onOpenChange(false);
		}
	};

	return (
		<AlertDialog open={open} onOpenChange={handleClose}>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>{t("deleteServiceType")}</AlertDialogTitle>
					<AlertDialogDescription>
						{t("deleteServiceTypeDescription", {
							name: serviceType.name,
						})}
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel disabled={isDeleting}>
						{t("cancel")}
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={handleDelete}
						disabled={isDeleting}
						className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
					>
						{isDeleting ? t("deleting") : t("delete")}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}
