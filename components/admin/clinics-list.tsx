"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
	Trash,
	Edit,
	Eye,
	CheckCircle,
	XCircle,
	Star,
	Check,
	X,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
	DataTable,
	type Column,
	type FilterOption,
	type Action,
} from "./data-table";
import { StatusBadge } from "./status-badge";
import { AdminClinicEditDialog } from "./clinic-edit-dialog";
import { api } from "@/lib/trpc/react";
import type { ClinicWithUser, ClinicStatus } from "@/lib/types/clinic";

interface AdminClinic extends ClinicWithUser {
	wilaya?: { name: string } | null;
	commune?: { name: string } | null;
}

interface AdminClinicsListProps {
	limit?: number;
}

export function AdminClinicsList({ limit }: AdminClinicsListProps) {
	const t = useTranslations("admin.clinics");
	const tCommon = useTranslations("common");
	const { toast } = useToast();

	// State for pagination and filtering
	const [page, setPage] = useState(0);
	const [pageSize] = useState(limit || 20);
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState<ClinicStatus | "">("");
	const [featuredFilter, setFeaturedFilter] = useState<boolean | "">("");
	const [sortBy, setSortBy] = useState<
		"name" | "address" | "createdAt" | "featured" | "status"
	>("createdAt");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

	// Dialog states
	const [statusDialogOpen, setStatusDialogOpen] = useState(false);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [selectedClinic, setSelectedClinic] = useState<AdminClinic | null>(
		null
	);
	const [newStatus, setNewStatus] = useState<ClinicStatus>("pending");

	// Bulk operations state
	const [selectedClinics, setSelectedClinics] = useState<Set<number>>(
		new Set()
	);
	const [bulkApproveDialogOpen, setBulkApproveDialogOpen] = useState(false);
	const [bulkRejectDialogOpen, setBulkRejectDialogOpen] = useState(false);

	// Query for clinics data
	const {
		data: clinicsData,
		isLoading,
		error,
		refetch,
	} = api.clinics.adminList.useQuery({
		limit: pageSize,
		offset: page * pageSize,
		search: search || undefined,
		featured: featuredFilter === "" ? undefined : featuredFilter,
		status: statusFilter === "" ? undefined : statusFilter,
		sortBy,
		sortOrder,
	});

	// Mutations
	const updateStatusMutation = api.clinics.updateStatus.useMutation({
		onSuccess: () => {
			toast({
				title: tCommon("success"),
				description: t("statusUpdated"),
			});
			setStatusDialogOpen(false);
			setSelectedClinic(null);
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const setFeaturedMutation = api.clinics.setFeatured.useMutation({
		onSuccess: () => {
			toast({
				title: tCommon("success"),
				description: t("featuredUpdated"),
			});
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const deleteMutation = api.clinics.delete.useMutation({
		onSuccess: () => {
			toast({
				title: tCommon("success"),
				description: t("clinicDeleted"),
			});
			setDeleteDialogOpen(false);
			setSelectedClinic(null);
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Bulk operations mutations
	const bulkApproveMutation = api.clinics.bulkApprove.useMutation({
		onSuccess: (result) => {
			toast({
				title: tCommon("success"),
				description: t("bulk.approved", { count: result.updatedCount }),
			});
			setBulkApproveDialogOpen(false);
			setSelectedClinics(new Set());
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const bulkRejectMutation = api.clinics.bulkReject.useMutation({
		onSuccess: (result) => {
			toast({
				title: tCommon("success"),
				description: t("bulk.rejected", { count: result.updatedCount }),
			});
			setBulkRejectDialogOpen(false);
			setSelectedClinics(new Set());
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Bulk selection helpers
	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			const allIds = new Set(
				clinicsData?.clinics.map((clinic) => clinic.id) || []
			);
			setSelectedClinics(allIds);
		} else {
			setSelectedClinics(new Set());
		}
	};

	const handleSelectClinic = (clinicId: number, checked: boolean) => {
		const newSelected = new Set(selectedClinics);
		if (checked) {
			newSelected.add(clinicId);
		} else {
			newSelected.delete(clinicId);
		}
		setSelectedClinics(newSelected);
	};

	const isAllSelected =
		clinicsData?.clinics.length > 0 &&
		selectedClinics.size === clinicsData.clinics.length;
	const isIndeterminate =
		selectedClinics.size > 0 &&
		selectedClinics.size < (clinicsData?.clinics.length || 0);

	// Table columns configuration
	const columns: Column<AdminClinic>[] = [
		{
			key: "select",
			label: "",
			render: (clinic) => (
				<Checkbox
					checked={selectedClinics.has(clinic.id)}
					onCheckedChange={(checked) =>
						handleSelectClinic(clinic.id, checked as boolean)
					}
				/>
			),
			className: "w-12",
		},
		{
			key: "clinic",
			label: t("columns.clinic"),
			sortable: true,
			render: (clinic) => (
				<div className="space-y-1">
					<div className="font-medium">{clinic.name}</div>
					<div className="text-sm text-muted-foreground">
						{clinic.user.name} ({clinic.user.email})
					</div>
				</div>
			),
		},
		{
			key: "location",
			label: t("columns.location"),
			render: (clinic) => (
				<div className="text-sm">
					<div>{clinic.address}</div>
					{clinic.wilaya && (
						<div className="text-muted-foreground">
							{clinic.wilaya.name}
							{clinic.commune && `, ${clinic.commune.name}`}
						</div>
					)}
				</div>
			),
		},
		{
			key: "status",
			label: t("columns.status"),
			sortable: true,
			render: (clinic) => (
				<StatusBadge
					status={clinic.status}
					variant={
						clinic.status === "approved"
							? "success"
							: clinic.status === "rejected"
								? "destructive"
								: "warning"
					}
				>
					{t(`status.${clinic.status}`)}
				</StatusBadge>
			),
		},
		{
			key: "featured",
			label: t("columns.featured"),
			sortable: true,
			render: (clinic) => (
				<div className="flex items-center gap-2">
					{clinic.featured && (
						<Star className="h-4 w-4 text-yellow-500" />
					)}
					<span className="text-sm">
						{clinic.featured ? tCommon("yes") : tCommon("no")}
					</span>
				</div>
			),
		},
		{
			key: "createdAt",
			label: t("columns.createdAt"),
			sortable: true,
			render: (clinic) => (
				<div className="text-sm text-muted-foreground">
					{new Date(clinic.createdAt).toLocaleDateString()}
				</div>
			),
		},
	];

	// Table actions configuration
	const actions: Action<AdminClinic>[] = [
		{
			label: t("actions.viewProfile"),
			icon: <Eye className="h-4 w-4" />,
			onClick: (clinic) => {
				// Navigate to clinic profile
				window.open(`/clinic/${clinic.user.slug}`, "_blank");
			},
		},
		{
			label: t("actions.edit"),
			icon: <Edit className="h-4 w-4" />,
			onClick: (clinic) => {
				setSelectedClinic(clinic);
				setEditDialogOpen(true);
			},
		},
		{
			label: t("actions.changeStatus"),
			icon: <CheckCircle className="h-4 w-4" />,
			onClick: (clinic) => {
				setSelectedClinic(clinic);
				setNewStatus(
					clinic.status === "approved" ? "rejected" : "approved"
				);
				setStatusDialogOpen(true);
			},
		},
		{
			label: t("actions.feature"),
			icon: <Star className="h-4 w-4" />,
			onClick: (clinic) => {
				setFeaturedMutation.mutate({
					clinicId: clinic.id,
					featured: !clinic.featured,
				});
			},
		},
		{
			label: t("actions.delete"),
			icon: <Trash className="h-4 w-4" />,
			variant: "destructive" as const,
			onClick: (clinic) => {
				setSelectedClinic(clinic);
				setDeleteDialogOpen(true);
			},
		},
	];

	// Filter options configuration
	const filters: FilterOption[] = [
		{
			key: "status",
			label: t("filters.status"),
			options: [
				{ label: tCommon("all"), value: "" },
				{ label: t("status.pending"), value: "pending" },
				{ label: t("status.approved"), value: "approved" },
				{ label: t("status.rejected"), value: "rejected" },
			],
		},
		{
			key: "featured",
			label: t("filters.featured"),
			options: [
				{ label: tCommon("all"), value: "" },
				{ label: tCommon("yes"), value: "true" },
				{ label: tCommon("no"), value: "false" },
			],
		},
	];

	// Event handlers
	const handleStatusUpdate = () => {
		if (!selectedClinic) return;

		updateStatusMutation.mutate({
			clinicId: selectedClinic.id,
			status: newStatus,
		});
	};

	const handleDelete = () => {
		if (!selectedClinic) return;

		deleteMutation.mutate({
			clinicId: selectedClinic.id,
		});
	};

	const handleSort = (column: string, order: "asc" | "desc") => {
		setSortBy(column as typeof sortBy);
		setSortOrder(order);
	};

	const handlePageChange = (newPage: number) => {
		setPage(newPage);
	};

	if (error) {
		return (
			<div className="flex items-center justify-center min-h-[400px] border rounded-md bg-muted/10">
				<div className="text-center space-y-2">
					<p className="text-lg font-medium text-destructive">
						{tCommon("error")}
					</p>
					<p className="text-sm text-muted-foreground">
						{error.message}
					</p>
					<Button onClick={() => refetch()} variant="outline">
						{tCommon("retry")}
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Bulk Actions */}
			{selectedClinics.size > 0 && (
				<div className="flex items-center gap-2 p-4 bg-muted rounded-lg">
					<span className="text-sm text-muted-foreground">
						{t("bulk.selected", { count: selectedClinics.size })}
					</span>
					<div className="flex gap-2 ml-auto">
						<Button
							size="sm"
							variant="outline"
							onClick={() => setBulkApproveDialogOpen(true)}
							disabled={bulkApproveMutation.isPending}
						>
							<Check className="h-4 w-4 mr-2" />
							{t("bulk.approve")}
						</Button>
						<Button
							size="sm"
							variant="outline"
							onClick={() => setBulkRejectDialogOpen(true)}
							disabled={bulkRejectMutation.isPending}
						>
							<X className="h-4 w-4 mr-2" />
							{t("bulk.reject")}
						</Button>
						<Button
							size="sm"
							variant="ghost"
							onClick={() => setSelectedClinics(new Set())}
						>
							{tCommon("clear")}
						</Button>
					</div>
				</div>
			)}

			<DataTable
				data={clinicsData?.clinics || []}
				columns={columns}
				actions={actions}
				filters={filters}
				filterValues={{
					status: statusFilter,
					featured:
						featuredFilter === "" ? "" : featuredFilter.toString(),
				}}
				onFilterChange={(key, value) => {
					if (key === "status") {
						setStatusFilter(value as ClinicStatus | "");
					} else if (key === "featured") {
						setFeaturedFilter(value === "" ? "" : value === "true");
					}
				}}
				searchValue={search}
				onSearchChange={setSearch}
				searchPlaceholder={t("searchPlaceholder")}
				loading={isLoading}
				sortBy={sortBy}
				sortOrder={sortOrder}
				onSortChange={(key, order) => {
					setSortBy(key as typeof sortBy);
					setSortOrder(order);
				}}
				pagination={
					clinicsData
						? {
								total: clinicsData.total,
								limit: pageSize,
								offset: page * pageSize,
								onPageChange: (offset) =>
									setPage(offset / pageSize),
							}
						: undefined
				}
				emptyMessage={t("noClinics")}
			/>

			{/* Status Change Dialog */}
			<Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{t("dialogs.changeStatus.title")}
						</DialogTitle>
						<DialogDescription>
							{t("dialogs.changeStatus.description", {
								clinicName: selectedClinic?.name,
							})}
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<label className="text-sm font-medium">
								{t("dialogs.changeStatus.newStatus")}
							</label>
							<Select
								value={newStatus}
								onValueChange={(value) =>
									setNewStatus(value as ClinicStatus)
								}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="pending">
										{t("status.pending")}
									</SelectItem>
									<SelectItem value="approved">
										{t("status.approved")}
									</SelectItem>
									<SelectItem value="rejected">
										{t("status.rejected")}
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setStatusDialogOpen(false)}
						>
							{tCommon("cancel")}
						</Button>
						<Button
							onClick={handleStatusUpdate}
							disabled={updateStatusMutation.isPending}
						>
							{updateStatusMutation.isPending
								? tCommon("updating")
								: tCommon("update")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("dialogs.delete.title")}</DialogTitle>
						<DialogDescription>
							{t("dialogs.delete.description", {
								clinicName: selectedClinic?.name,
							})}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDeleteDialogOpen(false)}
						>
							{tCommon("cancel")}
						</Button>
						<Button
							variant="destructive"
							onClick={handleDelete}
							disabled={deleteMutation.isPending}
						>
							{deleteMutation.isPending
								? tCommon("deleting")
								: tCommon("delete")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Dialog */}
			<AdminClinicEditDialog
				open={editDialogOpen}
				onOpenChange={setEditDialogOpen}
				clinic={selectedClinic}
				onSuccess={() => {
					// Refetch data after successful edit
					refetch();
				}}
			/>

			{/* Bulk Approve Dialog */}
			<Dialog
				open={bulkApproveDialogOpen}
				onOpenChange={setBulkApproveDialogOpen}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{t("bulk.dialogs.approve.title")}
						</DialogTitle>
						<DialogDescription>
							{t("bulk.dialogs.approve.description", {
								count: selectedClinics.size,
							})}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setBulkApproveDialogOpen(false)}
						>
							{tCommon("cancel")}
						</Button>
						<Button
							onClick={() =>
								bulkApproveMutation.mutate({
									clinicIds: Array.from(selectedClinics),
								})
							}
							disabled={bulkApproveMutation.isPending}
						>
							{bulkApproveMutation.isPending
								? tCommon("approving")
								: t("bulk.approve")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Bulk Reject Dialog */}
			<Dialog
				open={bulkRejectDialogOpen}
				onOpenChange={setBulkRejectDialogOpen}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{t("bulk.dialogs.reject.title")}
						</DialogTitle>
						<DialogDescription>
							{t("bulk.dialogs.reject.description", {
								count: selectedClinics.size,
							})}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setBulkRejectDialogOpen(false)}
						>
							{tCommon("cancel")}
						</Button>
						<Button
							variant="destructive"
							onClick={() =>
								bulkRejectMutation.mutate({
									clinicIds: Array.from(selectedClinics),
								})
							}
							disabled={bulkRejectMutation.isPending}
						>
							{bulkRejectMutation.isPending
								? tCommon("rejecting")
								: t("bulk.reject")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
