import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { auth } from "@/lib/auth";
import { AdminServiceTypesClient } from "@/components/admin/service-types/admin-service-types-client";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: string }>;
}): Promise<Metadata> {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "admin" });

	return {
		title: t("serviceTypes.title"),
		description: t("serviceTypes.description"),
	};
}

export default async function AdminServiceTypesPage({
	params,
}: {
	params: Promise<{ lang: string }>;
}) {
	const { lang } = await params;
	const session = await auth();

	// Check if user is authenticated and has admin role
	if (!session?.user || session.user.role !== "admin") {
		notFound();
	}

	const t = await getTranslations({ locale: lang, namespace: "admin" });

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
					{t("serviceTypes.title")}
				</h1>
				<p className="mt-2 text-gray-600 dark:text-gray-400">
					{t("serviceTypes.description")}
				</p>
			</div>

			<AdminServiceTypesClient />
		</div>
	);
}
